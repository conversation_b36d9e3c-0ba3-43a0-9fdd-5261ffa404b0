<?php

namespace App\Models\ES;

use Lib\Elasticsearch\Model;

class Paper extends Model
{
    protected $index = 'paper';
    protected $type = 'filtered';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'paper_keyword',
        'paper_brand_service_id',
        'es_id',
        'paper_sub_message',
        'paper_brand_id',
        'paper_state',
        'paper_content_updated',
        'paper_tag_keywords',
        'paper_parent_service_id',
        'paper_parent_id',
        'paper_total_share',
        'paper_sub_service_id',
        'paper_created',
        'paper_message_3',
        'paper_point',
        'paper_message_4',
        'paper_per_neutral',
        'paper_id',
        'paper_content_from_name',
        'paper_content_from_id',
        'paper_total_page_like',
        'paper_message_1',
        'paper_message_2',
        'paper_message_0',
        'paper_is_pin',
        'paper_own_type',
        'paper_sub_status',
        'paper_status_0',
        'paper_status_1',
        'paper_page_id',
        'paper_promote',
        'paper_sub_brand_service_id',
        'paper_lead',
        'paper_primary',
        'paper_mention_type',
        'paper_content_created',
        'paper_title',
        'paper_status_4',
        'paper_status_2',
        'paper_price',
        'paper_status_3',
        'paper_concern_point',
        'paper_tag',
        'paper_child_count',
        'paper_is_duplicated',
        'paper_per_positive',
        'paper_page_name',
        'paper_total_like',
        'paper_type',
        'paper_category_url',
        'paper_is_updated',
        'paper_service_id',
        'paper_sub_parent_service_id',
        'paper_is_delete',
        'paper_object_id',
        'paper_url',
        'paper_is_backup',
        'paper_per_negative',
        'paper_parent_object_id',
        'paper_count_word',
        'paper_category_name',
        'paper_area',
        'paper_page',
        'paper_page_type',
        'paper_language',
        'paper_campaign',
        'paper_group_id',
        'paper_group_name',
        'paper_image',
    ];
    protected $appends = ['content_created'];
    protected $casts = [
        'paper_content_created' => 'datetime',
    ];
    public function getSubBrandServiceIdAttribute()
    {
        return $this->paper_sub_brand_service_id;
    }
    public function getObjectIdAttribute()
    {
        return $this->paper_object_id;
    }
    public function getContentCreatedAttribute()
    {
        return $this->paper_content_created;
    }
}
