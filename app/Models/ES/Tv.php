<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Tv extends Model
{
    protected $index = 'tv';
    protected $type = 'filtered';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'tv_keyword',
        'tv_brand_service_id',
        'es_id',
        'tv_sub_message',
        'tv_brand_id',
        'tv_state',
        'tv_content_updated',
        'tv_tag_keywords',
        'tv_parent_service_id',
        'tv_parent_id',
        'tv_total_share',
        'tv_sub_service_id',
        'tv_created',
        'tv_message_3',
        'tv_point',
        'tv_message_4',
        'tv_per_neutral',
        'tv_id',
        'tv_content_from_name',
        'tv_content_from_id',
        'tv_total_page_like',
        'tv_message_1',
        'tv_message_2',
        'tv_message_0',
        'tv_is_pin',
        'tv_own_type',
        'tv_sub_status',
        'tv_status_0',
        'tv_status_1',
        'tv_page_id',
        'tv_promote',
        'tv_sub_brand_service_id',
        'tv_lead',
        'tv_primary',
        'tv_mention_type',
        'tv_content_created',
        'tv_title',
        'tv_status_4',
        'tv_status_2',
        'tv_price',
        'tv_status_3',
        'tv_concern_point',
        'tv_tag',
        'tv_spot_time',
        'tv_is_duplicated',
        'tv_per_positive',
        'tv_page_name',
        'tv_total_like',
        'tv_type',
        'tv_is_updated',
        'tv_service_id',
        'tv_sub_parent_service_id',
        'tv_is_delete',
        'tv_object_id',
        'tv_url',
        'tv_is_backup',
        'tv_per_negative',
        'tv_parent_object_id',
        'tv_count_word',
        'tv_category_name',
        'tv_category_id',
        'tv_language',
        'tv_campaign',
        'tv_author',
        'tv_group_id',
        'tv_group_name',
        'tv_video',

    ];
    protected $casts = [
        'content_created' => 'datetime',
    ];
}
