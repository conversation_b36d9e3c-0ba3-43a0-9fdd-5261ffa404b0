<?php

namespace App\Console\Commands;

use App\Enum\StateEnum;
use App\Models\ES\Paper;
use App\Models\ES\ReviewGoogleBusiness;
use App\Models\ES\Tiktok;
use App\Models\ES\Tv;
use App\Models\ES\Tvplus;
use App\Models\ES\Website;
use App\Models\ES\Youtube;
use App\Models\ES\Zalo;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use QuickChart;

class BIMDaily extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:bim';
    public $benchmarkStartTime;
    public $benchmarkStartMemory;

    public $quickChartApi = 'q-hrcceah0ie6s4bbn9no37w24h139su6k';
    public
    const MODELS = [
        Tvplus::class,
        Youtube::class,
        Tiktok::class,
        Zalo::class,
        Paper::class,
        Tv::class,
        Website::class,
        ReviewGoogleBusiness::class,
    ];
    const MAIN_PROJECT = [
        'id' => 41777,
        'name' => 'Thương hiệu BIM và lãnh đạo',
    ];
    const PROJECT = [
        41777 => 'Thương hiệu BIM và lãnh đạo',
        41778 => 'SKY M Hạ Long',
        41779 => 'Sora Bay HaLong',
        41780 => 'Marina Bayfront District',
        41781 => 'Grand Bay Halong Villas',
        41782 => 'Sailing Club Residences Ha Long Bay',
        41783 => 'Citadines Marina Halong',
        41784 => 'Aqua City HaLong',
        41785 => 'Horizon Bay',
        41786 => 'Hạ Long Marina',
        41787 => 'Intercontinental Ha Long Bay',

        41788 => 'Phu Quoc Marina',
        41789 => 'Phu Quoc Waterfront',
        41790 => 'Sailing Club Signature Resort Phu Quoc',
        41791 => 'Park Hyatt Phu Quoc Residences',

        41792 => 'Thanh Xuân Valley',
        41795 => 'Valley Park Residences',
        41796 => 'Valley Town',
        41793 => 'Intercontinental Thanh Xuan Valley',
        41794 => 'Spring Residences',

        41797 => 'Khu nghỉ dưỡng cao cấp Vĩnh Hy',


    ];
    const PROJECT_HL = [
        41778 => 'SKY M Hạ Long',
        41779 => 'Sora Bay HaLong',
        41780 => 'Marina Bayfront District',
        41781 => 'Grand Bay Halong Villas',
        41782 => 'Sailing Club Residences Ha Long Bay',
        41783 => 'Citadines Marina Halong',
        41784 => 'Aqua City HaLong',
        41785 => 'Horizon Bay',
        41786 => 'Hạ Long Marina',
        41787 => 'Intercontinental Ha Long Bay',
        41799 => 'Sun Centro Town',
        41800 => 'Sun Elite City Hạ Long',
        41803 => 'Xanh Island Cát Bà',
    ];
    const PROJECT_PQ = [
        41788 => 'Phu Quoc Marina',
        41789 => 'Phu Quoc Waterfront',
        41790 => 'Sailing Club Signature Resort Phu Quoc',
        41791 => 'Park Hyatt Phu Quoc Residences',
    ];
    const PROJECT_VP = [
        41792 => 'Thanh Xuân Valley',
        41795 => 'Valley Park Residences',
        41796 => 'Valley Town',
        41793 => 'Intercontinental Thanh Xuan Valley',
        41794 => 'Spring Residences',
    ];

    public Collection $facebookData;
    public Collection $youtubeData;
    public Collection $tiktokData;
    public Collection $zaloData;
    public Collection $paperData;
    public Collection $tvData;
    public Collection $online;
    public Collection $blog;
    public Collection $reviewGoogleBusinessData;

    public Collection $flatData;
    public $nextRow = 0;
    const competitor = [
        41799 => 'Sun Centro Town',
        41800 => 'Sun Elite City Hạ Long',
        41803 => 'Xanh Island Cát Bà',
    ];
    const OTHER = [
        41801 => 'Eco Retreat',
        41802 => 'Waterpoint Nam Long',
    ];
    private $startTime;
    private $endTime;

    // template

    /**
     * Generate random unique colors for charts
     * @param int $count Number of colors needed
     * @return array Array of hex color codes
     */
    private function generateRandomColors(int $count): array
    {
        // Predefined nice colors for better visual (loại bỏ duplicate)
        $predefinedColors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#C9CBCF', '#E7E9ED', '#71B37C', '#FFA726',
            '#AB47BC', '#26C6DA', '#66BB6A', '#EF5350', '#42A5F5',
            '#FFCA28', '#26A69A', '#8E24AA', '#D32F2F', '#1976D2',
            '#388E3C', '#F57C00', '#7B1FA2', '#303F9F', '#5D4037'
        ];

        $colors = [];
        $usedColors = [];

        // First, use predefined colors
        for ($i = 0; $i < min($count, count($predefinedColors)); $i++) {
            $colors[] = $predefinedColors[$i];
            $usedColors[] = $predefinedColors[$i];
        }

        // Generate additional random colors if needed
        $maxAttempts = 1000; // Tránh infinite loop
        $attempts = 0;

        while (count($colors) < $count && $attempts < $maxAttempts) {
            $attempts++;

            // Generate random RGB values với khoảng cách lớn hơn
            $r = rand(0, 255);
            $g = rand(0, 255);
            $b = rand(0, 255);

            $hexColor = sprintf('#%02X%02X%02X', $r, $g, $b);

            // Kiểm tra không trùng hoàn toàn và đủ khác biệt
            if (!in_array($hexColor, $usedColors) && $this->isColorDistinct($hexColor, $usedColors)) {
                $colors[] = $hexColor;
                $usedColors[] = $hexColor;
            }
        }

        // Nếu vẫn không đủ màu, dùng HSV để generate màu đều
        if (count($colors) < $count) {
            $remaining = $count - count($colors);
            $hueStep = 360 / $remaining;

            for ($i = 0; $i < $remaining; $i++) {
                $hue = ($i * $hueStep) % 360;
                $saturation = rand(70, 100) / 100; // 70-100%
                $value = rand(70, 100) / 100; // 70-100%

                $hexColor = $this->hsvToHex($hue, $saturation, $value);

                if (!in_array($hexColor, $usedColors)) {
                    $colors[] = $hexColor;
                    $usedColors[] = $hexColor;
                }
            }
        }

        return array_slice($colors, 0, $count);
    }

    /**
     * Check if a color is distinct enough from existing colors
     * @param string $newColor New color in hex format
     * @param array $existingColors Array of existing colors
     * @return bool True if color is distinct enough
     */
    private function isColorDistinct(string $newColor, array $existingColors): bool
    {
        $newRgb = $this->hexToRgb($newColor);

        foreach ($existingColors as $existingColor) {
            $existingRgb = $this->hexToRgb($existingColor);

            // Calculate color distance using Euclidean distance
            $distance = sqrt(
                pow($newRgb['r'] - $existingRgb['r'], 2) +
                pow($newRgb['g'] - $existingRgb['g'], 2) +
                pow($newRgb['b'] - $existingRgb['b'], 2)
            );

            // If distance is too small, colors are too similar
            if ($distance < 100) { // Adjust threshold as needed
                return false;
            }
        }

        return true;
    }

    /**
     * Convert hex color to RGB array
     * @param string $hex Hex color code
     * @return array RGB values
     */
    private function hexToRgb(string $hex): array
    {
        $hex = ltrim($hex, '#');

        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        ];
    }

    /**
     * Convert HSV to Hex color
     * @param float $h Hue (0-360)
     * @param float $s Saturation (0-1)
     * @param float $v Value (0-1)
     * @return string Hex color code
     */
    private function hsvToHex(float $h, float $s, float $v): string
    {
        $c = $v * $s;
        $x = $c * (1 - abs(fmod($h / 60, 2) - 1));
        $m = $v - $c;

        if ($h < 60) {
            $r = $c; $g = $x; $b = 0;
        } elseif ($h < 120) {
            $r = $x; $g = $c; $b = 0;
        } elseif ($h < 180) {
            $r = 0; $g = $c; $b = $x;
        } elseif ($h < 240) {
            $r = 0; $g = $x; $b = $c;
        } elseif ($h < 300) {
            $r = $x; $g = 0; $b = $c;
        } else {
            $r = $c; $g = 0; $b = $x;
        }

        $r = ($r + $m) * 255;
        $g = ($g + $m) * 255;
        $b = ($b + $m) * 255;

        return sprintf('#%02X%02X%02X', (int)$r, (int)$g, (int)$b);
    }


    public function handle()
    {
        $this->startBenchmark();
        $now = Carbon::now(); // thời điểm hiện tại
        $this->startTime = $now->copy()->subDay()->setTime(15, 0)->format('Y-m-d H:i:s');
        $this->endTime = $now->copy()->setTime(15, 0)->format('Y-m-d H:i:s');

        $this->facebookData = $this->queryProject(Tvplus::class);
        $this->youtubeData = $this->queryProject(Youtube::class);
        $this->tiktokData = $this->queryProject(Tiktok::class);
        $this->zaloData = $this->queryProject(Zalo::class);
        $this->paperData = $this->queryProject(Paper::class);
        $this->tvData = $this->queryProject(Tv::class);
        $websiteData = $this->queryProject(Website::class);
        $this->online = $websiteData->where('web_status_4', 0);
        $this->blog = $websiteData->where('web_status_4', 1);
        $this->reviewGoogleBusinessData = $this->queryProject(ReviewGoogleBusiness::class);
        $this->flatData = collect([
            $this->facebookData,
            $this->youtubeData,
            $this->tiktokData,
            $this->zaloData,
            $this->paperData,
            $this->tvData,
            $this->online,
            $this->blog,
            $this->reviewGoogleBusinessData,
        ])->flatten(1)->values();


        $this->writeReport();


        $this->endBenchmark([]);
    }

    protected function writeReport()
    {
        $filePath = storage_path('app/Form_2.xlsx');
        $spreadsheet = IOFactory::load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $spreadsheet->getDefaultStyle()
            ->getFont()
            ->setName('Arial')
            ->setSize(14)
            ->setBold(true);
        $sheet->getStyle('A3')->applyFromArray([
            'font' => [
                'color' => [
                    'rgb' => '492CA0', // không cần dấu #
                ],
            ],
        ]);
        $spreadsheet->setActiveSheetIndex(0);
        $sheet->setTitle('Báo cáo ngày');
        $sheet->setCellValue('F5', 'DAILY REPORT');
        $sheet->setCellValue('F6', 'Ngày: ' . date('d/m/Y', strtotime('-1 day')) . " - " . date('d/m/Y'));
        $sheet->setCellValue('C9', "BIM GROUP");
        $sheet->setCellValue('C10', 'Ngày: ' . date('d/m/Y'));
        $sheet->setCellValue('C11', 'Báo cáo ngày');
        [$styleBlueColor, $styleBoldText] = $this->style();

        // start col
        $i = 15;
        $col = 1;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $i;
        $sheet->setCellValue($cell, 'I. THÔNG TIN CỤ THỂ VỀ ' . strtoupper('BIM GROUP'));
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);


        // viết 2 table ngang hàng nhau
        $startRow = 16;
        $leftTableNextRow = $this->tableSpecificInformation($sheet, $startRow, 2); // Cột B (2)
        $rightTableNextRow = $this->tableSpecificEmotionalIndex($sheet, $startRow, 6); // Cột F (6)

        // Lấy dòng tiếp theo sau cả 2 table
        $nextRow = max($leftTableNextRow, $rightTableNextRow);

        // Thêm pie chart Share of Voice ở cột C, dưới các table
        $nextRow = $this->pieSpecificShareOfVoice($sheet, $nextRow, 3); // Cột C (3)
        $nextRow = $this->barSpecificNewsPlatform($sheet, $nextRow, 3); // Cột C (3)
        $nextRow = $this->lineSpecificHourlyTrend($sheet, $nextRow, 3, self::PROJECT); // Cột C (3)
        $nextRow = $this->emotionalRatio($sheet, $nextRow, 3, self::PROJECT); // Cột C (3)
        $nextRow = $this->tableDetailPlatform($sheet, $nextRow, 2, self::PROJECT); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->tableSentiment($sheet, $nextRow, 2, self::PROJECT); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->tableNegative($sheet, $nextRow, 2, self::PROJECT); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->topNewsSource($sheet, $nextRow, 2, self::PROJECT); // Cột C (3)
        //$nextRow += 2;
        $nextRow = $this->topINTERACTIVERESOURCES($sheet, $nextRow, 2, self::PROJECT); // Cột C (3)
        $nextRow += 2;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $nextRow;
        $sheet->setCellValue($cell, 'II. TỔNG QUAN BIM GROUP VÀ ĐỐI THỦ');
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);
        $nextRow += 2;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $nextRow;
        $sheet->setCellValue($cell, '1. Khu vực HẠ LONG');
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);
        $nextRow += 2;
        $nextRow = $this->tableDetailPlatform($sheet, $nextRow, 2, self::PROJECT_HL); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->tableSentiment($sheet, $nextRow, 2, self::PROJECT_HL); // Cột C (3)
        $nextRow += 2;

        $nextRow = $this->pieSpecificShareOfVoice($sheet, $nextRow, 3, self::PROJECT_HL); // Cột C (3)
        $nextRow = $this->barSpecificNewsPlatform($sheet, $nextRow, 3, self::PROJECT_HL); // Cột C (3)
        $nextRow = $this->lineSpecificHourlyTrend($sheet, $nextRow, 3, self::PROJECT_HL); // Cột C (3)
        $nextRow = $this->emotionalRatio($sheet, $nextRow, 3, self::PROJECT_HL); // Cột C (3)

        // phu quoc

        $nextRow += 2;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $nextRow;
        $sheet->setCellValue($cell, '2. Khu vực PHÚ QUỐC');
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);
        $nextRow += 2;
        $nextRow = $this->tableDetailPlatform($sheet, $nextRow, 2, self::PROJECT_PQ); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->tableSentiment($sheet, $nextRow, 2, self::PROJECT_PQ); // Cột C (3)
        $nextRow += 2;

        $nextRow = $this->pieSpecificShareOfVoice($sheet, $nextRow, 3, self::PROJECT_PQ); // Cột C (3)
        $nextRow = $this->barSpecificNewsPlatform($sheet, $nextRow, 3, self::PROJECT_PQ); // Cột C (3)
        $nextRow = $this->lineSpecificHourlyTrend($sheet, $nextRow, 3, self::PROJECT_PQ); // Cột C (3)
        $nextRow = $this->emotionalRatio($sheet, $nextRow, 3, self::PROJECT_PQ); // Cột C (3)

        // vinh phuc

        $nextRow += 2;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $nextRow;
        $sheet->setCellValue($cell, '3. Khu vực VĨNH PHÚC');
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);
        $nextRow += 2;
        $nextRow = $this->tableDetailPlatform($sheet, $nextRow, 2, self::PROJECT_VP); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->tableSentiment($sheet, $nextRow, 2, self::PROJECT_VP); // Cột C (3)
        $nextRow += 2;

        $nextRow = $this->pieSpecificShareOfVoice($sheet, $nextRow, 3, self::PROJECT_VP); // Cột C (3)
        $nextRow = $this->barSpecificNewsPlatform($sheet, $nextRow, 3, self::PROJECT_VP); // Cột C (3)
        $nextRow = $this->lineSpecificHourlyTrend($sheet, $nextRow, 3, self::PROJECT_VP); // Cột C (3)
        $nextRow = $this->emotionalRatio($sheet, $nextRow, 3, self::PROJECT_VP); // Cột C (3)

        //other
        $nextRow += 2;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $nextRow;
        $sheet->setCellValue($cell, '4. Khu vực KHÁC (LONG AN)');
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);
        $nextRow += 2;
        $nextRow = $this->tableDetailPlatform($sheet, $nextRow, 2, self::OTHER); // Cột C (3)
        $nextRow += 2;
        $nextRow = $this->tableSentiment($sheet, $nextRow, 2, self::OTHER); // Cột C (3)
        $nextRow += 2;

        $nextRow = $this->pieSpecificShareOfVoice($sheet, $nextRow, 3, self::OTHER); // Cột C (3)
        $nextRow = $this->barSpecificNewsPlatform($sheet, $nextRow, 3, self::OTHER); // Cột C (3)
        $nextRow = $this->lineSpecificHourlyTrend($sheet, $nextRow, 3, self::OTHER); // Cột C (3)
        $nextRow = $this->emotionalRatio($sheet, $nextRow, 3, self::OTHER);

        $this->writeData($spreadsheet);

        $newFile = storage_path('app/my_report.xlsx');
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($newFile);
    }

    protected function writeData($spreadsheet)
    {
        $headers = [
            "Thương hiệu", "Phương tiện", "Nguồn phát hành", "Nội dung", "Loại tin",
            "Ngày phát hành", "Like", "Share", "Comment"
        ];

        // Tạo các sheet
        $sheetBIM = new Worksheet($spreadsheet, "Thương hiệu BIM và Lãnh Đạo");
        $sheetHL = new Worksheet($spreadsheet, "Khu vực Hạ Long");
        $sheetPQ = new Worksheet($spreadsheet, "Khu vực Phú Quốc");
        $sheetVP = new Worksheet($spreadsheet, "Khu vực Vĩnh Phúc");
        $sheetOther = new Worksheet($spreadsheet, "Khu vực Khác");

        // Thêm sheet vào file trước khi format
        $spreadsheet->addSheet($sheetBIM);
        $spreadsheet->addSheet($sheetHL);
        $spreadsheet->addSheet($sheetPQ);
        $spreadsheet->addSheet($sheetVP);
        $spreadsheet->addSheet($sheetOther);

        // Thêm headers và format cho từng sheet
        $sheets = [$sheetBIM, $sheetHL, $sheetPQ, $sheetVP, $sheetOther];
        foreach ($sheets as $sheet) {
            $sheet->fromArray([$headers], null, 'A1');

            // Format header row - font Cambria, size 12, bold, borders, màu hồng nhạt
            $headerRange = 'A1:' . Coordinate::stringFromColumnIndex(count($headers)) . '1';
            $sheet->getStyle($headerRange)->applyFromArray([
                'font' => [
                    'name' => 'Cambria',
                    'size' => 12,
                    'bold' => true,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F4C2C2'], // Màu hồng nhạt như trong ảnh
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ]);

            // Tăng chiều cao header row (padding 10)
            $sheet->getRowDimension(1)->setRowHeight(30);

            // Auto-size columns với giới hạn cho cột "Nội dung"
            foreach (range('A', Coordinate::stringFromColumnIndex(count($headers))) as $colIndex => $col) {
                if ($col === 'D') { // Cột "Nội dung" là cột D (thứ 4)
                    $sheet->getColumnDimension($col)->setWidth(30); // Giảm xuống 30 characters để ngắn gọn hơn
                } else {
                    $sheet->getColumnDimension($col)->setAutoSize(true);
                }
            }
        }

        $data = $this->mapPlatForm();
        $BIMData = [];
        $HLData = [];
        $PQData = [];
        $VPData = [];
        $OtherData = [];
        foreach ($data as $platform => $collect) {
            $BIMData[$platform] = $collect->where('sub_brand_service_id', self::MAIN_PROJECT['id']);
            $HLData[$platform] = $collect->whereIn('sub_brand_service_id', array_keys(self::PROJECT_HL));
            $PQData[$platform] = $collect->whereIn('sub_brand_service_id', array_keys(self::PROJECT_PQ));
            $VPData[$platform] = $collect->whereIn('sub_brand_service_id', array_keys(self::PROJECT_VP));
            $OtherData[$platform] = $collect->whereIn('sub_brand_service_id', array_keys(self::OTHER));
        }
        $label = self::allProject();

        $writer = function ($sheet, $data) use ($headers, $label) {
            $startRow = 2;
            $hasData = false;

            // Check if there's any data
            foreach ($data as $platform => $collect) {
                if ($collect->count() > 0) {
                    $hasData = true;
                    break;
                }
            }

            // If no data, show "Không có dữ liệu"
            if (!$hasData) {
                $sheet->setCellValue('A2', 'Không có dữ liệu');
                $sheet->mergeCells('A2:' . Coordinate::stringFromColumnIndex(count($headers)) . '2');
                $sheet->getStyle('A2')->applyFromArray([
                    'font' => [
                        'name' => 'Cambria',
                        'size' => 12,
                        'italic' => true,
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => '000000'],
                        ],
                    ],
                ]);
                return;
            }

            foreach ($data as $platform => $collect) {
                foreach ($collect as $row) {
                    $rowData = [
                        $label[$row->sub_brand_service_id] ?? '',
                        $platform,
                        $row->page_name ?? '',
                        $row->message ?? '',
                        StateEnum::listVietnam()[$row->state] ?? '',
                        $row->content_created ?? '',
                        (string)($row->total_like ?? 0), // Ép kiểu string để hiển thị 0
                        (string)($row->total_share ?? 0), // Ép kiểu string để hiển thị 0
                        (string)($row->child_count ?? 0), // Ép kiểu string để hiển thị 0
                    ];

                    $sheet->fromArray([$rowData], null, "A{$startRow}");

                    // Format content row - font Cambria, size 12, NO BOLD, borders
                    $rowRange = 'A' . $startRow . ':' . Coordinate::stringFromColumnIndex(count($headers)) . $startRow;
                    $sheet->getStyle($rowRange)->applyFromArray([
                        'font' => [
                            'name' => 'Cambria',
                            'size' => 12,
                            'bold' => false, // Đảm bảo data không bold
                        ],
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000'],
                            ],
                        ],
                        'alignment' => [
                            'vertical' => Alignment::VERTICAL_CENTER,
                        ],
                    ]);

                    // KHÔNG dùng text wrapping để giữ 1 dòng, Excel sẽ tự động hiện "..." khi quá dài
                    $sheet->getStyle("D{$startRow}")->getAlignment()->setWrapText(false);

                    $message = $row->message ?? '';

                    $link = "";
                    if (get_class($row) === Tvplus::class && !empty($row->fb_id)) {
                        $link = "https://www.fb.com/" . $row->fb_id;
                    }
                    if (get_class($row) === Website::class) {
                        $link = $row->web_url;
                    }
                    if (get_class($row) === Tiktok::class) {
                        $link = "https://www.tiktok.com/@{$row->content_from_id}/video/{$row->fb_id}";
                    }
                    if (get_class($row) == Paper::class) {
                        $link = "http://social.monitaz.com/detail/index/{$row->es_id}?_channel=2";
                    }
                    if (get_class($row) == Tv::class) {
                        $link = "https://data.monitaz.asia/detail/index/{$row->es_id}?_channel=3";
                    }

                    if ($link) {
                        $sheet->getCell("D{$startRow}")->getHyperlink()->setUrl($link);
                        $sheet->getStyle("D{$startRow}")->getFont()->getColor()->setARGB(
                            \PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLUE);
                        $sheet->getStyle("D{$startRow}")->getFont()->setUnderline(true);
                    }
                    $startRow++;
                }
            }
        };

        $writer($sheetBIM, $BIMData);
        $writer($sheetHL, $HLData);
        $writer($sheetPQ, $PQData);
        $writer($sheetVP, $VPData);
        $writer($sheetOther, $OtherData);

        // Set sheet đầu tiên active
        $spreadsheet->setActiveSheetIndex(0);
    }

    protected static function allProject()
    {
        return self::PROJECT + self::OTHER + self::competitor;
    }

    protected function getDataOther()
    {
        $startTime = $this->startTime;
        $endTime = $this->endTime;
        $query = function ($model) use ($startTime, $endTime) {
            return $model::query()
                ->whereIn('object_id', [58593, 51068])
                ->whereIn('brand_id', [10586])
                ->whereIn('sub_brand_service_id', [41801, 41802])
                ->when($startTime, fn($query) => $query->where('content_created', '>=', $startTime))
                ->when($endTime, fn($query) => $query->where('content_created', '<=', $endTime))
                ->all(1500);
        };
        $this->facebookData = $query(Tvplus::class);
        $this->youtubeData = $query(Youtube::class);
        $this->tiktokData = $query(Tiktok::class);
        $this->zaloData = $query(Zalo::class);
        $this->paperData = $query(Paper::class);
        $this->tvData = $query(Tv::class);
        $websiteData = $query(Website::class);
        $this->online = $websiteData->where('web_status_4', 0);
        $this->blog = $websiteData->where('web_status_4', 1);
        $this->reviewGoogleBusinessData = $query(ReviewGoogleBusiness::class);
        $this->flatData = collect([
            $this->facebookData,
            $this->youtubeData,
            $this->tiktokData,
            $this->zaloData,
            $this->paperData,
            $this->tvData,
            $this->online,
            $this->blog,
            $this->reviewGoogleBusinessData,
        ])->flatten(1)->values();
    }

    protected function tableSpecificInformation($sheet, int $startRow = 16, int $startCol = 2): int
    {
        $infoHeaders = ['Lượng tin', 'Thương hiệu BIM và lãnh đạo', 'Các dự án'];
        $mainFacebook = $this->facebookData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainYoutube = $this->youtubeData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainTiktok = $this->tiktokData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainZalo = $this->zaloData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainPaper = $this->paperData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainTv = $this->tvData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainOnline = $this->online->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainWebsite = $this->blog->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainReviewGoogleBusiness = $this->reviewGoogleBusinessData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
        $mainTotal = array_sum([
            $mainFacebook,
            $mainYoutube,
            $mainOnline,
            $mainWebsite,
            $mainZalo,
            $mainTiktok,
            $mainReviewGoogleBusiness,
            $mainPaper,
            $mainTv,
        ]);

        $otherFacebook = $this->facebookData
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->count();
        $otherYoutube = $this->youtubeData
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherOnline = $this->online
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherWebsite = $this->blog
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherZalo = $this->zaloData
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherTiktok = $this->tiktokData
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherReviewGoogleBusiness = $this->reviewGoogleBusinessData
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherPaper = $this->paperData
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherTv = $this->tvData
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
        $otherTotal = array_sum([
            $otherFacebook,
            $otherYoutube,
            $otherOnline,
            $otherWebsite,
            $otherZalo,
            $otherTiktok,
            $otherReviewGoogleBusiness,
            $otherPaper,
            $otherTv,
        ]);

        $rows = [
            ['Facebook', $mainFacebook, $otherFacebook],
            ['Youtube', $mainYoutube, $otherYoutube],
            ['Online', $mainOnline, $otherOnline],
            ['Blog/Forum/Website', $mainWebsite, $otherWebsite],
            ['Zalo', $mainZalo, $otherZalo],
            ['Tiktok', $mainTiktok, $otherTiktok],
            ['Reviewmap', $mainReviewGoogleBusiness, $otherReviewGoogleBusiness],
            ['Paper', $mainPaper, $otherPaper],
            ['Tv', $mainTv, $otherTv],
            ['Tổng', $mainTotal, $otherTotal]
        ];
        // Vẽ table tự động với tham số truyền vào
        return $this->drawTable($sheet, $infoHeaders, $rows, $startRow, $startCol);
    }

    protected function tableSpecificEmotionalIndex($sheet, int $startRow = 16, int $startCol = 6): int
    {
        $headers = [
            'Chỉ số cảm xúc',
            'Thương hiệu BIM và lãnh đạo',
            'Các dự án'
        ];

        // Tính toán chỉ số cảm xúc từ data thực tế
        $mainPositive = $this->flatData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])
            ->where('state', 1)
            ->count();
        $mainNegative = $this->flatData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])
            ->where('state', 2)
            ->count();
        $mainNeutral = $this->flatData->where('sub_brand_service_id', self::MAIN_PROJECT['id'])
            ->where('state', 0)
            ->count();
        $mainEmotionalIndex = ($mainPositive + $mainPositive) > 0
            ? max(($mainPositive - $mainPositive) / ($mainPositive + $mainPositive), 0)
            : 0;;

        $otherPositive = $this->flatData
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])
            ->where('state', 1)
            ->count();
        $otherNegative = $this->flatData->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('state', 2)
            ->count();
        $otherNeutral = $this->flatData->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])
            ->whereNotIn('sub_brand_service_id', [
                    ...array_keys(self::competitor),
                    ...array_keys(self::OTHER)
                ]
            )
            ->where('state', 0)
            ->count();

        $otherEmotionalIndex = ($otherPositive + $otherNegative) > 0
            ? max(($otherPositive - $otherNegative) / ($otherPositive + $otherNegative), 0)
            : 0;;

        $rows = [
            ['Positive', $mainPositive, $otherPositive],
            ['Negative', $mainNegative, $otherNegative],
            ['Neutral', $mainNeutral, $otherNeutral],
            ['Chỉ số cảm xúc', $mainEmotionalIndex, $otherEmotionalIndex],
        ];

        // Vẽ table với border và màu header khác để phân biệt
        return $this->drawTableWithBorder($sheet, $headers, $rows, $startRow, $startCol); // Màu xanh nhạt
    }

    /**
     * Vẽ pie chart Share of Voice
     * @param $sheet
     * @param int $startRow - Dòng bắt đầu
     * @param int $startCol - Cột bắt đầu (mặc định là C = 3)
     * @param array $projects - Danh sách projects (mặc định dùng self::PROJECT)
     * @return int - Trả về dòng tiếp theo sau chart
     */
    protected function pieSpecificShareOfVoice($sheet, int $startRow, int $startCol = 3, array $projects = null): int
    {
        if ($projects === null) {
            $projects = self::PROJECT;
        }
        $qc = new QuickChart([
            'width' => 1200,  // Tăng độ phân giải
            'height' => 400,  // Tăng độ phân giải
            'format' => 'png'
        ]);
        $qc->setApiKey($this->quickChartApi);

        $rows = [];
        foreach ($projects as $k => $project) {
            $total = $this->flatData->where('sub_brand_service_id', $k)->count();
            $rows[$project] = $total;
        }
        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => array_values($rows),
                    'backgroundColor' => $this->generateRandomColors(count($rows)),
                ]],
                'labels' => array_keys($rows),
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));

        // Tạo và lưu ảnh pie chart với chất lượng cao
        $time = time();
        $pathToPieImg = storage_path('app/pie_chart_hd_' . Str::uuid()->toString() . '.png');
        $qc->toFile($pathToPieImg);

        // Chèn ảnh vào Excel với cài đặt tối ưu chất lượng
        $drawing = new Drawing();
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);

        // Tính toán vị trí chính xác
        $chartCell = Coordinate::stringFromColumnIndex($startCol) . ($startRow + 2);
        $drawing->setCoordinates($chartCell);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);  // Giảm kích thước hiển thị để tăng DPI
        $drawing->setResizeProportional(false);
        $drawing->setWorksheet($sheet);

        // Tính toán dòng tiếp theo (chart cao khoảng 15 rows)
        $chartHeightInRows = 13;
        $nextRow = $startRow + 2 + $chartHeightInRows;


        return $nextRow;
    }

    protected function barSpecificNewsPlatform($sheet, int $startRow, int $startCol = 3, array $projects = null): int
    {

        $datasets = [];
        if ($projects === null) {
            $mainData = [];
            $otherData = [];
            foreach ($this->mapPlatForm() as $platform => $items) {
                $mainData[] = $items->where('sub_brand_service_id', self::MAIN_PROJECT['id'])->count();
                $otherData[] = $items
                    ->whereNotIn('sub_brand_service_id', [
                            ...array_keys(self::competitor),
                            ...array_keys(self::OTHER)
                        ]
                    )
                    ->where('sub_brand_service_id', '!=', self::MAIN_PROJECT['id'])->count();
            }
            $datasets = [
                [
                    'label' => 'Thương hiệu và lãnh đạo',
                    'data' => $mainData,
                    'fill' => true,
                ],
                [
                    'label' => 'Các ngành còn lại',
                    'data' => $otherData,
                    'fill' => true,
                ],
            ];
        }

        if ($projects !== null) {
            foreach ($projects as $k => $v) {
                $d = [];

                foreach ($this->mapPlatForm() as $platform) {
                    $count = $platform
                        ->where('sub_brand_service_id', $k)
                        ->count();
                    $d[] = $count;
                }

                $datasets[] = [
                    'label' => $v,
                    'data' => $d,
                    'fill' => true,
                ];
            }
        }

        $platforms = array_keys($this->mapPlatForm());
        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 1200,  // Tăng độ phân giải
            'height' => 400,  // Tăng độ phân giải
            'format' => 'png'
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/' . Str::uuid()->toString() . '.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $chartCell = Coordinate::stringFromColumnIndex($startCol) . ($startRow + 2);
        $drawing->setCoordinates($chartCell);
        $drawing->setPath($pathToPieImg);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setResizeProportional(false);
        $drawing->setWorksheet($sheet);
        $chartHeightInRows = 13;
        return $startRow + 2 + $chartHeightInRows;
    }

    protected function lineSpecificHourlyTrend($sheet, int $startRow, int $startCol = 3, array $projects = null): int
    {
        $groupedByHour = $this->flatData->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $colors = $this->generateRandomColors(count($projects));
        $i = 0;
        $datasets = collect($projects)->map(function ($projectName, $projectId) use ($hours, $groupedByHour,&$i,$colors) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();
            $r =  [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
                'backgroundColor' => $colors[$i],
                'borderColor' => $colors[$i],
            ];
            $i++;
            return $r;
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 1200,
            'height' => 400
        ]);
        $qc->setApiKey($this->quickChartApi);
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/' . Str::uuid()->toString() . '.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $chartCell = Coordinate::stringFromColumnIndex($startCol) . ($startRow + 2);
        $drawing->setCoordinates($chartCell);
        $drawing->setPath($pathToPieImg);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setResizeProportional(false);
        $drawing->setWorksheet($sheet);
        $chartHeightInRows = 13;
        return $startRow + 2 + $chartHeightInRows;
    }

    protected function emotionalRatio($sheet, int $startRow, int $startCol = 3, array $projects = null): int
    {
        $counts = [];
        foreach ($projects as $projectId => $projectName) {
            $projectItems = $this->flatData->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();


            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => $total > 0 ? round($positive / $total * 100, 2) : 0,
                'neutral' => $total > 0 ? round($neutral / $total * 100, 2) : 0,
                'negative' => $total > 0 ? round($negative / $total * 100, 2) : 0,
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];
        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 1200,  // Tăng độ phân giải
            'height' => 400,  // Tăng độ phân giải
            'format' => 'png'
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $pathToPieImg = storage_path('app/' . Str::uuid()->toString() . '.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $chartCell = Coordinate::stringFromColumnIndex($startCol) . ($startRow + 2);
        $drawing->setCoordinates($chartCell);
        $drawing->setPath($pathToPieImg);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setResizeProportional(false);
        $drawing->setWorksheet($sheet);
        $chartHeightInRows = 13;
        return $startRow + 2 + $chartHeightInRows;
    }

    protected function tableDetailPlatform($sheet, int $startRow, int $startCol = 2, array $projects = null): int
    {
        $headers = [
            'Đối tượng',
            ...array_keys($this->mapPlatForm()),
            'Tổng'

        ];
        $rows = [];
        foreach ($projects as $projectId => $projectName) {
            $counters = [];
            foreach ($this->mapPlatForm() as $platform => $items) {
                $counters[] = $items->where('sub_brand_service_id', $projectId)->count();
            }
            $row = [
                $projectName,
                ...$counters,
                array_sum($counters)
            ];
            $rows[] = $row;
        }
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }
        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);

        // Thêm hàng "Tỉ lệ %"
        $grandTotal = end($totals);
        $ratios = array_map(function ($value) use ($grandTotal) {
            return $grandTotal > 0 ? round($value / $grandTotal * 100, 2) . '%' : '0%';
        }, $totals);
        $rows[] = array_merge(['Tỉ lệ %'], $ratios);
        return $this->drawTable($sheet, $headers, $rows, $startRow, $startCol);
    }

    protected function tableSentiment($sheet, int $startRow, int $startCol = 2, array $projects = null)
    {
        $headers = ['Sentiment', 'Positive', 'Negative', 'Neutral', 'Chỉ số cảm xúc'];
        $rows = [];
        $collect = collect($this->mapPlatForm());
        foreach ($projects as $projectId => $projectName) {
            $positive = $collect->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $collect->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $collect->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        return $this->drawTable($sheet, $headers, $rows, $startRow, $startCol);

    }

    protected function tableNegative($sheet, int $startRow, int $startCol = 2, array $projects = null): int
    {
        $headers = [
            'Đối tượng', 'Nền tảng', 'Top nguồn', 'Nội dung',
            'Time', 'Sentiment', 'Like', 'Share/View', 'Comment', 'Tổng tương tác'
        ];


        $rows = [];


        // Sử dụng method helper để vẽ table với tiêu đề
        return $this->drawTableWithTitle(
            $sheet,
            'NGUỒN TIN TIÊU CỰC ĐÁNG LƯU Ý',
            $headers,
            $rows,
            $startRow,
            $startCol,
            'FF0000', // Màu đỏ cho tiêu đề negative
        );
    }

    protected function topNewsSource($sheet, int $startRow, int $startCol = 2, array $projects = null): int
    {
        // Tạo headers động dựa trên platforms có data
        $headers = ['Đối tượng'];
        $availablePlatforms = [];

        foreach ($this->mapPlatForm() as $platformId => $platformData) {
            if ($platformData->isNotEmpty()) {
                $hasData = false;
                foreach ($projects as $projectId => $projectName) {
                    if ($platformData->where('sub_brand_service_id', $projectId)->isNotEmpty()) {
                        $hasData = true;
                        break;
                    }
                }
                if ($hasData) {
                    $availablePlatforms[$platformId] = $platformId;
                    $headers[] = $platformId; // Tên platform
                    $headers[] = 'Số lượng'; // Số lượng tin
                }
            }
        }

        // Tạo rows data - mỗi nguồn tin là một row riêng
        $rows = [];
        foreach ($projects as $projectId => $projectName) {
            $projectSources = []; // Lưu tất cả sources của project này

            // Lấy data từ tất cả platforms với thông tin link
            foreach ($availablePlatforms as $platformId => $platformName) {
                $topSources = $this->top10ByPageWithLinks($this->mapPlatForm()[$platformId], $projectId);
                // Đảm bảo data là array với key-value pairs và link
                $projectSources[$platformId] = $topSources->toArray();
            }

            // Tìm số nguồn tin nhiều nhất để tạo đủ rows
            $maxSources = 0;
            foreach ($projectSources as $sources) {
                $maxSources = max($maxSources, count($sources));
            }

            // Tạo rows cho project này
            for ($i = 0; $i < max(1, $maxSources); $i++) {
                $row = [];

                // Cột đầu tiên: tên dự án (chỉ hiện ở row đầu tiên)
                $row[] = ($i === 0) ? $projectName : '';

                // Các cột platforms
                foreach ($availablePlatforms as $platformId => $platformName) {
                    $sources = $projectSources[$platformId] ?? [];
                    $sourceItems = array_values($sources);

                    if (isset($sourceItems[$i])) {
                        $sourceData = $sourceItems[$i];
                        $sourceName = $sourceData['page_name'] ?? '';
                        $sourceCount = $sourceData['count'] ?? '';
                        $sourceLink = $sourceData['link'] ?? '';

                        // Thêm nguồn tin với link (nếu có) và số lượng
                        $row[] = [
                            'text' => $sourceName,
                            'link' => $sourceLink
                        ];
                        $row[] = (string)$sourceCount;
                    } else {
                        $row[] = '';
                        $row[] = '';
                    }
                }

                $rows[] = $row;
            }
        }
        return $this->drawTableWithTitleAndLinks(
            $sheet,
            'TOP NGUỒN TIN VỀ BIM GROUP',
            $headers,
            $rows,
            $startRow,
            $startCol,
            'ffff', // Màu xanh cho tiêu đề
        );
    }

    protected function topINTERACTIVERESOURCES($sheet, int $startRow, int $startCol = 2, array $projects = null): int
    {
        $headers = [
            'Đối tượng',
            'Nền tảng',
            'Top nguồn',
            'Nội dung',
            'Time',
            'Sentiment',
            'Like',
            'Share/View',
            'Comment',
            'Tổng tương tác'
        ];

        // Tạo rows data - mỗi nguồn tin là một row riêng
        $rows = [];

        foreach ($projects as $projectId => $projectName) {
            $platformRows = [];
            foreach ($this->mapPlatForm() as $platform => $collection) {
                $grouped = $collection
                    ->where('sub_brand_service_id', $projectId)
                    ->groupBy('page_name')
                    ->map(function ($items, $pageName) {
                        $total = $items->sum(fn($item) => $item->total_share + $item->total_like + $item->child_count);
                        $topItem = $items->sortByDesc(fn($item) => $item->total_share + $item->total_like + $item->child_count)->first();

                        // Tạo link cho Top nguồn (dựa trên content_from_id)
                        $sourceLink = '';
                        if ($topItem && !empty($topItem->content_from_id)) {
                            $contentFromId = $topItem->content_from_id;
                            if (get_class($topItem) === Tvplus::class) {
                                $sourceLink = "https://www.facebook.com/" . $contentFromId;
                            } elseif (get_class($topItem) === Youtube::class) {
                                $sourceLink = "https://www.youtube.com/channel/" . $contentFromId;
                            } elseif (get_class($topItem) === Tiktok::class) {
                                $sourceLink = "https://www.tiktok.com/@" . $contentFromId;
                            } elseif (get_class($topItem) === Website::class) {
                                $sourceLink = $topItem->web_url ?? '';
                            } elseif (get_class($topItem) === Paper::class) {
                                $sourceLink = "http://social.monitaz.com/detail/index/{$topItem->es_id}?_channel=2";
                            } elseif (get_class($topItem) === Tv::class) {
                                $sourceLink = "https://data.monitaz.asia/detail/index/{$topItem->es_id}?_channel=3";
                            }
                        }

                        // Tạo link cho Nội dung (dựa trên fb_id và platform)
                        $contentLink = '';
                        if ($topItem) {
                            if (get_class($topItem) === Tvplus::class && !empty($topItem->fb_id)) {
                                $contentLink = "https://www.fb.com/" . $topItem->fb_id;
                            } elseif (get_class($topItem) === Website::class) {
                                $contentLink = $topItem->web_url ?? '';
                            } elseif (get_class($topItem) === Tiktok::class) {
                                $contentLink = "https://www.tiktok.com/@{$topItem->content_from_id}/video/{$topItem->fb_id}";
                            } elseif (get_class($topItem) === Paper::class) {
                                $contentLink = "http://social.monitaz.com/detail/index/{$topItem->es_id}?_channel=2";
                            } elseif (get_class($topItem) === Tv::class) {
                                $contentLink = "https://data.monitaz.asia/detail/index/{$topItem->es_id}?_channel=3";
                            }
                        }

                        return [
                            'page_name' => $pageName,
                            'source_link' => $sourceLink,
                            'content' => $topItem->message,
                            'content_link' => $contentLink,
                            'time' => $topItem->content_created,
                            'sentiment' => StateEnum::listVietnam()[$topItem->state],
                            'like' => $topItem->total_like,
                            'share' => $topItem->total_share,
                            'comment' => $topItem->child_count,
                            'total' => $total,
                        ];
                    })
//                    ->filter(fn($item) => $item['total'] > 0)
//                    ->sortByDesc('total')
                    ->take(10)
                    ->values();
                $platformRows[$platform] = $grouped->toArray();
            }
            $rows[$projectName] = $platformRows;
        }
        $startRow = $startRow + 2;
        $titleCell = 'B' . $startRow;
        $sheet->setCellValue($titleCell, 'TOP NGUỒN TƯƠNG TÁC VỀ BIM GROUP.');
        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $startRow = $startRow + 1;
        $originalStartCol = $startCol; // Lưu lại startCol gốc
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($startCol);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $startCol++;
        }
        // Sửa lại cách tính headerRange
        $headerRange = Coordinate::stringFromColumnIndex($originalStartCol) . $startRow . ':' .
            Coordinate::stringFromColumnIndex($originalStartCol + count($headers) - 1) . $startRow;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
        $currentRow = $startRow + 1;

        foreach ($rows as $projectName => $platformRows) {
            // Tính tổng số dòng để merge Đối tượng
            $totalRowsForProject = collect($platformRows)->sum(fn($p) => count($p));

            $projectStartRow = $currentRow;

            foreach ($platformRows as $platform => $pages) {
                $platformStartRow = $currentRow;
                $pages = collect($pages)->sortByDesc('total')->toArray();
                foreach ($pages as $page) {
                    // Tạo data row
                    $rowData = [
                        $projectName,
                        ucfirst($platform),
                        $page['page_name'],
                        $page['content'],
                        $page['time'],
                        $page['sentiment'],
                        $page['like'] ?? 0,
                        $page['share'] ?? 0,
                        $page['comment'] ?? 0,
                        $page['total'] ?? 0,
                    ];

                    $sheet->fromArray([$rowData], null, 'B' . $currentRow, true);

                    // Thêm link cho cột "Top nguồn" (cột C)
                    if (!empty($page['source_link'])) {
                        $sheet->getCell('D' . $currentRow)->getHyperlink()->setUrl($page['source_link']);
                        $sheet->getStyle('D' . $currentRow)->getFont()->getColor()->setARGB('FF0000FF');
                        $sheet->getStyle('D' . $currentRow)->getFont()->setUnderline(true);
                    }

                    // Thêm link cho cột "Nội dung" (cột D)
                    if (!empty($page['content_link'])) {
                        $sheet->getCell('E' . $currentRow)->getHyperlink()->setUrl($page['content_link']);
                        $sheet->getStyle('E' . $currentRow)->getFont()->getColor()->setARGB('FF0000FF');
                        $sheet->getStyle('E' . $currentRow)->getFont()->setUnderline(true);
                    }

                    $currentRow++;
                }

                // Merge cột Nền tảng
                if (count($pages) > 1) {
                    $sheet->mergeCells("C{$platformStartRow}:C" . ($currentRow - 1));
                    $sheet->getStyle("C{$platformStartRow}:C" . ($currentRow - 1))
                        ->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                }
            }

            // Merge cột Đối tượng
            if ($totalRowsForProject > 1) {
                $sheet->mergeCells("B{$projectStartRow}:B" . ($currentRow - 1));
                $sheet->getStyle("B{$projectStartRow}:B" . ($currentRow - 1))
                    ->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            }
        }

//        // Style borders cho toàn bộ bảng
        $lastCol = Coordinate::stringFromColumnIndex($originalStartCol + count($headers) - 1);
        $lastRow = $currentRow - 1;
        $tableRange = Coordinate::stringFromColumnIndex($originalStartCol) . $startRow . ":{$lastCol}{$lastRow}";
        $sheet->getStyle($tableRange)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ]
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);
        return $currentRow;
    }


    private function top10ByPage(Collection $collection, int|string $subBrandId)
    {
        return $collection
            ->where('sub_brand_service_id', $subBrandId)
            ->groupBy('page_name')
            ->map(fn($items) => $items->count())
            ->sortDesc()
            ->take(10);
    }

    /**
     * Get top 10 sources by page with links from content_from_id
     */
    private function top10ByPageWithLinks(Collection $collection, int|string $subBrandId)
    {
        return $collection
            ->where('sub_brand_service_id', $subBrandId)
            ->groupBy('page_name')
            ->map(function($items, $pageName) {
                $count = $items->count();
                $firstItem = $items->first();
                $link = '';

                // Tạo link dựa trên content_from_id và platform
                if ($firstItem && !empty($firstItem->content_from_id)) {
                    $contentFromId = $firstItem->content_from_id;

                    // Xác định platform và tạo link tương ứng
                    if (get_class($firstItem) === Tvplus::class) {
                        $link = "https://www.facebook.com/" . $contentFromId;
                    } elseif (get_class($firstItem) === Youtube::class) {
                        $link = "https://www.youtube.com/channel/" . $contentFromId;
                    } elseif (get_class($firstItem) === Tiktok::class) {
                        $link = "https://www.tiktok.com/@" . $contentFromId;
                    } elseif (get_class($firstItem) === Website::class) {
                        $link = $firstItem->web_url ?? '';
                    } elseif (get_class($firstItem) === Paper::class) {
                        $link = "http://social.monitaz.com/detail/index/{$firstItem->es_id}?_channel=2";
                    } elseif (get_class($firstItem) === Tv::class) {
                        $link = "https://data.monitaz.asia/detail/index/{$firstItem->es_id}?_channel=3";
                    }
                }

                return [
                    'count' => $count,
                    'link' => $link,
                    'page_name' => $pageName
                ];
            })
            ->sortByDesc('count')
            ->take(10);
    }

    /**
     * Tạo hyperlink trong Excel cell
     * @param $sheet
     * @param string $cell - Vị trí cell (ví dụ: 'B5')
     * @param string $text - Text hiển thị
     * @param string $url - URL link
     */
    private function addHyperlink($sheet, string $cell, string $text, string $url): void
    {
        if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
            $sheet->getCell($cell)->getHyperlink()->setUrl($url);
            $sheet->setCellValue($cell, $text);
            $sheet->getStyle($cell)->applyFromArray([
                'font' => [
                    'color' => ['rgb' => '0000FF'],
                    'underline' => true
                ]
            ]);
        } else {
            $sheet->setCellValue($cell, $text);
        }
    }

    /**
     * Kiểm tra xem text có chứa URL không
     * @param string $text
     * @return bool
     */
    private function containsUrl(string $text): bool
    {
        return preg_match('/https?:\/\/[^\s]+/', $text) === 1;
    }

    /**
     * Thêm multiple hyperlinks vào cell (cho trường hợp có nhiều nguồn tin)
     * @param $sheet
     * @param string $cell
     * @param string $text
     */
    private function addMultipleHyperlinks($sheet, string $cell, string $text): void
    {
        // Tạm thời chỉ set text, có thể mở rộng sau để handle multiple links
        $sheet->setCellValue($cell, $text);
        $sheet->getStyle($cell)->applyFromArray([
            'font' => [
                'color' => ['rgb' => '0000FF'],
                'size' => 10
            ]
        ]);
    }

    /**
     * Tạo link cho nguồn tin với format đẹp
     * @param string $sourceName
     * @param int $count
     * @param string|null $url
     * @return string
     */
    private function formatSourceWithLink(string $sourceName, int $count, ?string $url = null): string
    {
        $formatted = $sourceName . ' (' . $count . ')';

        // Nếu có URL, có thể thêm indicator
        if (!empty($url)) {
            $formatted .= ' 🔗'; // Thêm icon link
        }

        return $formatted;
    }

    /**
     * Thêm tiêu đề cho table với style tùy chỉnh
     * @param $sheet
     * @param string $title - Tiêu đề
     * @param int $row - Dòng đặt tiêu đề
     * @param int $col - Cột đặt tiêu đề
     * @param string $color - Màu chữ (hex không có #)
     * @param int $fontSize - Kích thước font
     * @return void
     */
    protected function addTableTitle($sheet, string $title, int $row, int $col, string $color = '0070C0', int $fontSize = 12): void
    {
        $titleCell = Coordinate::stringFromColumnIndex($col) . $row;
        $sheet->setCellValue($titleCell, $title);
        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => $color],
                'size' => $fontSize
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
    }

    /**
     * Vẽ table với tiêu đề tự động
     * @param $sheet
     * @param string $title - Tiêu đề table
     * @param array $headers - Headers
     * @param array $rows - Data rows
     * @param int $startRow - Dòng bắt đầu
     * @param int $startCol - Cột bắt đầu
     * @param string $titleColor - Màu tiêu đề
     * @param string $borderStyle - Kiểu border
     * @param string $headerBgColor - Màu nền header
     * @return int - Dòng tiếp theo
     */
    protected function drawTableWithTitle($sheet, string $title, array $headers, array $rows, int $startRow, int $startCol = 2, string $titleColor = '0070C0', string $borderStyle = 'simple', string $headerBgColor = 'E7E6E6'): int
    {
        // Thêm tiêu đề
        $this->addTableTitle($sheet, $title, $startRow, $startCol, $titleColor);

        // Vẽ table từ dòng tiếp theo
        return $this->drawTableWithBorder($sheet, $headers, $rows, $startRow + 1, $startCol, $borderStyle, $headerBgColor);
    }

    /**
     * Vẽ table với tiêu đề và hỗ trợ link
     * @param $sheet
     * @param string $title - Tiêu đề table
     * @param array $headers - Headers
     * @param array $rows - Data rows
     * @param int $startRow - Dòng bắt đầu
     * @param int $startCol - Cột bắt đầu
     * @param string $titleColor - Màu tiêu đề
     * @param string $borderStyle - Kiểu border
     * @param string $headerBgColor - Màu nền header
     * @return int - Dòng tiếp theo
     */
    protected function drawTableWithTitleAndLinks($sheet, string $title, array $headers, array $rows, int $startRow, int $startCol = 2, string $titleColor = '0070C0', string $borderStyle = 'simple', string $headerBgColor = 'E7E6E6'): int
    {
        // Thêm tiêu đề
        $this->addTableTitle($sheet, $title, $startRow, $startCol, $titleColor);

        $currentRow = $startRow + 1;
        $totalCols = count($headers);

        // Vẽ header
        foreach ($headers as $colIndex => $header) {
            $cell = Coordinate::stringFromColumnIndex($startCol + $colIndex) . $currentRow;
            $sheet->setCellValue($cell, $header);
            $sheet->getStyle($cell)->applyFromArray([
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'color' => ['rgb' => $headerBgColor]
                ]
            ]);
        }

        $currentRow++; // Chuyển sang dòng tiếp theo

        // Vẽ data rows - mỗi nguồn tin là một row riêng
        $projectStartRows = []; // Lưu vị trí bắt đầu của mỗi project để merge sau
        $currentProject = '';
        $projectStartRow = $currentRow;

        foreach ($rows as $rowIndex => $row) {
            // Kiểm tra xem có phải row đầu tiên của project mới không
            if (!empty($row[0]) && $row[0] !== $currentProject) {
                // Merge cells cho project trước đó (nếu có)
                if (!empty($currentProject) && $currentRow > $projectStartRow) {
                    $projectCell = Coordinate::stringFromColumnIndex($startCol) . $projectStartRow;
                    $endCell = Coordinate::stringFromColumnIndex($startCol) . ($currentRow - 1);
                    if ($projectStartRow < $currentRow - 1) {
                        $sheet->mergeCells($projectCell . ':' . $endCell);
                        $sheet->getStyle($projectCell)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                    }
                }

                $currentProject = $row[0];
                $projectStartRow = $currentRow;
            }

            // Vẽ từng cell trong row
            foreach ($row as $colIndex => $cellValue) {
                $cell = Coordinate::stringFromColumnIndex($startCol + $colIndex) . $currentRow;

                // Xử lý cell value và link
                $displayText = '';
                $link = '';

                if (is_array($cellValue) && isset($cellValue['text'])) {
                    // Cell có link
                    $displayText = $cellValue['text'];
                    $link = $cellValue['link'] ?? '';
                } else {
                    // Cell thường
                    $displayText = (string)$cellValue;
                }

                $sheet->setCellValue($cell, $displayText);

                // Thêm hyperlink nếu có
                if (!empty($link)) {
                    $sheet->getCell($cell)->getHyperlink()->setUrl($link);
                }

                // Style cho cell
                $cellStyle = [
                    'alignment' => [
                        'horizontal' => $colIndex === 0 ? Alignment::HORIZONTAL_LEFT : Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                        'wrapText' => false
                    ]
                ];

                // Style đặc biệt cho cột đầu tiên (tên dự án)
                if ($colIndex === 0) {
                    $cellStyle['font'] = ['bold' => true, 'size' => 11];
                    $cellStyle['alignment']['horizontal'] = Alignment::HORIZONTAL_LEFT;
                }

                // Style cho cột nguồn tin (cột lẻ và không phải cột đầu)
                if ($colIndex > 0 && $colIndex % 2 === 1) {
                    $cellStyle['font'] = [
                        'color' => ['rgb' => !empty($link) ? '0000FF' : '000000'], // Màu xanh nếu có link
                        'size' => 10, // Font nhỏ hơn cho nguồn tin
                        'underline' => !empty($link) // Gạch chân nếu có link
                    ];
                    $cellStyle['alignment']['horizontal'] = Alignment::HORIZONTAL_LEFT;
                }

                // Style cho cột số lượng (cột chẵn và không phải cột đầu)
                if ($colIndex > 0 && $colIndex % 2 === 0) {
                    $cellStyle['font'] = ['size' => 10];
                    $cellStyle['alignment']['horizontal'] = Alignment::HORIZONTAL_CENTER;
                }

                $sheet->getStyle($cell)->applyFromArray($cellStyle);

                // Tăng chiều rộng cột
                $columnLetter = Coordinate::stringFromColumnIndex($startCol + $colIndex);
                if ($colIndex === 0) {
                    $sheet->getColumnDimension($columnLetter)->setWidth(25); // Cột tên dự án
                } elseif ($colIndex % 2 === 1) {
                    $sheet->getColumnDimension($columnLetter)->setWidth(20); // Cột nguồn tin
                } else {
                    $sheet->getColumnDimension($columnLetter)->setWidth(10); // Cột số lượng
                }
            }

            // Tăng chiều cao row
            $sheet->getRowDimension($currentRow)->setRowHeight(20);
            $currentRow++; // Chuyển đến row tiếp theo
        }

        // Merge cells cho project cuối cùng
        if (!empty($currentProject) && $currentRow > $projectStartRow) {
            $projectCell = Coordinate::stringFromColumnIndex($startCol) . $projectStartRow;
            $endCell = Coordinate::stringFromColumnIndex($startCol) . ($currentRow - 1);
            if ($projectStartRow < $currentRow - 1) {
                $sheet->mergeCells($projectCell . ':' . $endCell);
                $sheet->getStyle($projectCell)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            }
        }

        // Áp dụng border chung cho toàn bộ table
        $tableRange = Coordinate::stringFromColumnIndex($startCol) . ($startRow + 1) . ':' .
            Coordinate::stringFromColumnIndex($startCol + $totalCols - 1) . ($currentRow - 1);

        $this->applyTableBorder($sheet, $tableRange, $borderStyle);

        return $currentRow; // Trả về dòng tiếp theo
    }

    /**
     * Helper method để merge cells theo chiều dọc
     * @param $sheet
     * @param int $colIndex - Index của cột (0-based)
     * @param int $startRow - Row bắt đầu
     * @param int $endRow - Row kết thúc
     */
    private function mergeCellsVertical($sheet, int $colIndex, int $startRow, int $endRow): void
    {
        if ($startRow < $endRow) {
            $startCell = Coordinate::stringFromColumnIndex($colIndex) . $startRow;
            $endCell = Coordinate::stringFromColumnIndex($colIndex) . $endRow;
            $sheet->mergeCells($startCell . ':' . $endCell);
            $sheet->getStyle($startCell)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        }
    }

    /**
     * Method demo để tạo table với link mẫu
     * @param $sheet
     * @param int $startRow
     * @param int $startCol
     * @return int
     */
    protected function demoTableWithLinks($sheet, int $startRow, int $startCol = 2): int
    {
        $headers = ['Đối tượng', 'Facebook', 'Số lượng', 'Youtube', 'Số lượng'];

        // Format mới - mỗi nguồn tin là một row riêng
        $rows = [
            // Project 1: Thương hiệu BIM và lãnh đạo
            ['Thương hiệu BIM và lãnh đạo', 'Trình Quốc Cường', '2', 'stockbiz.vn', '3'],
            ['', 'Trần Tuấn Anh', '2', 'vnbiznews.vn', '2'],
            ['', 'Bách Trường', '2', 'marketimes.vn', '2'],
            ['', 'Thanh Reno', '1', 'lincoln.vn', '2'],
            ['', 'Anh Thiện', '1', 'saigoneconomy.vn', '2'],
            ['', 'Bảo Tiger', '1', 'dantri.com.vn', '1'],
            ['', 'Buy Phong', '1', '', ''],

            // Project 2: Spring Residences
            ['Spring Residences', 'Valley Town - Phố Á', '1', 'phumuon.baophap', '1'],
            ['', 'RealNEX', '1', 'danviet.vn', '1'],
            ['', 'Vũ Xuân Sơn', '1', '', ''],
            ['', 'Anh Ngọc Phạm', '4', '', ''],
            ['', 'Lê Thắng Lê', '3', '', ''],
            ['', 'Thái Hữu Dương', '2', '', ''],
        ];

        return $this->drawTableWithTitleAndLinks(
            $sheet,
            'DEMO: MỖI NGUỒN TIN MỘT Ô RIÊNG',
            $headers,
            $rows,
            $startRow,
            $startCol,
            '00AA00', // Màu xanh lá
            'simple',
            'E6FFE6'  // Nền xanh nhạt
        );
    }

    /**
     * Demo table cho interactive resources
     * @param $sheet
     * @param int $startRow
     * @param int $startCol
     * @return int
     */
    protected function demoInteractiveTable($sheet, int $startRow, int $startCol = 2): int
    {
        $headers = [
            'Đối tượng', 'Nền tảng', 'Top nguồn', 'Nội dung',
            'Time', 'Sentiment', 'Like', 'Share/View', 'Comment', 'Tổng tương tác'
        ];

        // Format mới - mỗi nguồn tin là một row riêng
        $rows = [
            // Project 1: Thương hiệu BIM và lãnh đạo
            ['Thương hiệu BIM và lãnh đạo', 'Facebook', 'VnExpress', 'Tin tức về dự án mới...', '2024-01-15', 'Tích cực', '150', '45', '23', '218'],
            ['', 'Facebook', 'Tuổi Trẻ', 'Phân tích thị trường BĐS...', '2024-01-14', 'Trung tính', '89', '12', '8', '109'],
            ['', 'Youtube', 'VTV News', 'Video giới thiệu dự án...', '2024-01-13', 'Tích cực', '234', '67', '45', '346'],
            ['', 'Youtube', 'VTC Now', 'Phỏng vấn lãnh đạo...', '2024-01-12', 'Tích cực', '123', '34', '19', '176'],

            // Project 2: Spring Residences
            ['Spring Residences', 'Facebook', 'Dân Trí', 'Mở bán căn hộ mới...', '2024-01-11', 'Tích cực', '98', '23', '15', '136'],
            ['', 'Facebook', 'Zing News', 'Tiến độ xây dựng...', '2024-01-10', 'Trung tính', '67', '18', '9', '94'],
            ['', 'Tiktok', 'Minh Trường', 'Review dự án...', '2024-01-09', 'Tích cực', '45', '12', '7', '64'],
        ];

        return $this->drawTableWithTitleAndLinks(
            $sheet,
            'DEMO: TOP NGUỒN TƯƠNG TÁC - MỖI NGUỒN MỘT Ô',
            $headers,
            $rows,
            $startRow,
            $startCol,
            'FF6600', // Màu cam
            'simple',
            'FFF2E6'  // Nền cam nhạt
        );
    }

    /**
     * Vẽ table tự động với header và rows
     * @param $sheet
     * @param array $headers - Mảng header
     * @param array $rows - Mảng 2 chiều chứa data rows
     * @param int $startRow - Dòng bắt đầu vẽ table
     * @param int $startCol - Cột bắt đầu (mặc định là B = 2)
     * @return int - Trả về dòng tiếp theo sau table để có thể vẽ tiếp
     */
    protected function drawTable($sheet, array $headers, array $rows, int $startRow, int $startCol = 2): int
    {

        $currentRow = $startRow;
        $totalCols = count($headers);
        $totalRows = count($rows) + 1; // +1 cho header

        // Vẽ header
        foreach ($headers as $colIndex => $header) {
            $cell = Coordinate::stringFromColumnIndex($startCol + $colIndex) . $currentRow;
            $sheet->setCellValue($cell, $header);
            $sheet->getStyle($cell)->applyFromArray([
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'color' => ['rgb' => 'E7E6E6'] // Màu nền header
                ]
            ]);
        }

        $currentRow++; // Chuyển sang dòng tiếp theo

        // Vẽ data rows
        foreach ($rows as $row) {
            foreach ($row as $colIndex => $cellValue) {
                $cell = Coordinate::stringFromColumnIndex($startCol + $colIndex) . $currentRow;
                $sheet->setCellValue($cell, $cellValue);
                $sheet->getStyle($cell)->applyFromArray([
                    'alignment' => [
                        'horizontal' => $colIndex === 0 ? Alignment::HORIZONTAL_LEFT : Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ]
                ]);
            }
            $currentRow++; // Tự động xuống dòng
        }

        // Áp dụng border chung cho toàn bộ table
        $tableRange = Coordinate::stringFromColumnIndex($startCol) . $startRow . ':' .
            Coordinate::stringFromColumnIndex($startCol + $totalCols - 1) . ($currentRow - 1);

        $this->applyTableBorder($sheet, $tableRange);

        return $currentRow; // Trả về dòng tiếp theo để có thể vẽ table khác
    }

    /**
     * Vẽ table với tùy chọn border style
     * @param $sheet
     * @param array $headers
     * @param array $rows
     * @param int $startRow
     * @param int $startCol
     * @param string $borderStyle - 'simple', 'thick', 'double'
     * @param string $headerBgColor - Màu nền header (hex không có #)
     * @return int
     */
    protected function drawTableWithBorder($sheet, array $headers, array $rows, int $startRow, int $startCol = 2, string $borderStyle = 'simple', string $headerBgColor = 'E7E6E6'): int
    {

        $currentRow = $startRow;
        $totalCols = count($headers);

        // Vẽ header với màu nền tùy chỉnh
        foreach ($headers as $colIndex => $header) {
            $cell = Coordinate::stringFromColumnIndex($startCol + $colIndex) . $currentRow;
            $sheet->setCellValue($cell, $header);
            $sheet->getStyle($cell)->applyFromArray([
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'color' => ['rgb' => $headerBgColor]
                ]
            ]);
        }

        $currentRow++; // Chuyển sang dòng tiếp theo

        // Vẽ data rows
        foreach ($rows as $row) {
            foreach ($row as $colIndex => $cellValue) {
                $cell = Coordinate::stringFromColumnIndex($startCol + $colIndex) . $currentRow;
                $sheet->setCellValue($cell, $cellValue);
                $sheet->getStyle($cell)->applyFromArray([
                    'alignment' => [
                        'horizontal' => $colIndex === 0 ? Alignment::HORIZONTAL_LEFT : Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ]
                ]);
            }
            $currentRow++; // Tự động xuống dòng
        }

        // Áp dụng border chung cho toàn bộ table
        $tableRange = Coordinate::stringFromColumnIndex($startCol) . $startRow . ':' .
            Coordinate::stringFromColumnIndex($startCol + $totalCols - 1) . ($currentRow - 1);

        $this->applyTableBorder($sheet, $tableRange, $borderStyle);

        return $currentRow; // Trả về dòng tiếp theo để có thể vẽ table khác
    }

    /**
     * Áp dụng border style cho table với nhiều tùy chọn
     * @param $sheet
     * @param string $range - Range của table (ví dụ: "B16:D25")
     * @param string $borderStyle - Kiểu border: 'simple', 'thick', 'double'
     */
    protected function applyTableBorder($sheet, string $range, string $borderStyle = 'simple'): void
    {
        $style = [
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ];

        $sheet->getStyle($range)->applyFromArray($style);
    }

    /**
     * Thêm một table mới với title và spacing tự động
     * @param $sheet
     * @param string $title - Tiêu đề table
     * @param array $headers - Mảng header
     * @param array $rows - Mảng 2 chiều chứa data rows
     * @param int $startRow - Dòng bắt đầu
     * @param int $startCol - Cột bắt đầu (mặc định là B = 2)
     * @param int $spacing - Khoảng cách trước title (mặc định là 2 dòng)
     * @param string $borderStyle - Kiểu border: 'simple', 'thick', 'double'
     * @param string $headerBgColor - Màu nền header
     * @return int - Trả về dòng tiếp theo
     */
    protected function addTableWithTitle($sheet, string $title, array $headers, array $rows, int $startRow, int $startCol = 2, int $spacing = 2, string $borderStyle = 'simple', string $headerBgColor = 'E7E6E6'): int
    {
        [$styleBlueColor, $styleBoldText] = $this->style();

        // Thêm spacing
        $currentRow = $startRow + $spacing;

        // Vẽ title
        $titleCell = Coordinate::stringFromColumnIndex($startCol) . $currentRow;
        $sheet->setCellValue($titleCell, $title);
        $sheet->getStyle($titleCell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);

        $currentRow++; // Xuống dòng sau title

        // Vẽ table với border tùy chỉnh
        return $this->drawTableWithBorder($sheet, $headers, $rows, $currentRow, $startCol, $borderStyle, $headerBgColor);
    }


    protected function style()
    {
        $styles = [
            'borders' => [
                'allBorders' => [ // Viết hoa camelCase
                    'style' => Border::BORDER_THIN
                ]
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'FFFFFF']
            ],
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleArrays = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleSmallText = [
            'font' => ['size' => 9]
        ];

        $styleRedColor = [
            'font' => ['color' => ['rgb' => 'FF0000']]
        ];

        $styleBlueColor = [
            'font' => ['color' => ['rgb' => '0070C0']]
        ];

        $styleLeftAlignment = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
        ];

        $styleRightAlignment = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT]
        ];

        $styleBoldText = [
            'font' => ['bold' => true]
        ];

        $styleAllBorders = [
            'borders' => [
                'allBorders' => ['style' => Border::BORDER_THIN]
            ]
        ];

        $styleItalicText = [
            'font' => ['italic' => true]
        ];

        $styleArray = [
            'borders' => [
                'allBorders' => ['style' => Border::BORDER_THIN]
            ],
            'font' => [
                'underline' => 'none',
                'color' => ['rgb' => '492CA0'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleText = [
            'borders' => [
                'allBorders' => ['style' => Border::BORDER_THIN]
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleWrapText = [
            'alignment' => [
                'wrapText' => true
            ]
        ];

        $styleCenterAlignment = [
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleLinkText = [
            'font' => [
                'color' => ['rgb' => '0000FF'],
                'size' => 11
            ]
        ];
        return [$styleBlueColor, $styleBoldText];
    }


    protected function queryProject($model, $start = null, $end = null): Collection
    {
        $start = $this->startTime;
        $end = $this->endTime;
        return $model::query()
            ->whereIn('object_id', [11440, 58593, 51068, 58591, 58592, 58595])
            ->whereIn('brand_id', [10586])
            ->whereIn('sub_brand_service_id', [
                ...array_keys(self::PROJECT),
                ...array_keys(self::OTHER),
                41799, 41800, 41803
            ])
            ->when($model == Website::class, fn($query) => $query->where('web_sub_parent_service_id', 1))
            ->when($model == Paper::class, fn($query) => $query->where('paper_sub_parent_service_id', 1))
            ->when($model == Tv::class, fn($query) => $query->where('tv_sub_parent_service_id', 1))
            ->when(!in_array($model, [Website::class, Paper::class, Tv::class]),
                fn($query) => $query->where('sub_parent_service_id', 1))
            ->when($start, fn($query) => $query->where('content_created', '>=', $start))
            ->when($end, fn($query) => $query->where('content_created', '<=', $end))
            ->all(1500);
    }


    protected function mapPlatForm()
    {
        return [
            'Facebook' => $this->facebookData,
            'Online' => $this->online,
            'Blog/Forum/Website' => $this->blog,
            'Youtube' => $this->youtubeData,
            'Zalo' => $this->zaloData,
            'Tiktok' => $this->tiktokData,
            'Review Map' => $this->reviewGoogleBusinessData,
            'Paper' => $this->paperData,
            'TV' => $this->tvData
        ];
    }

    protected function startBenchmark()
    {
        $this->benchmarkStartTime = microtime(true);
        $this->benchmarkStartMemory = memory_get_usage();
    }

    protected function endBenchmark($res)
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = $endTime - $this->benchmarkStartTime;
        $memoryUsed = $endMemory - $this->benchmarkStartMemory;
        $this->newLine();
        $this->line(sprintf(
            '⚡ <bg=bright-blue;fg=black> TIME: %s s </> <bg=bright-green;fg=black> MEM: %sMB </> <bg=bright-magenta;fg=black> ROWS: %s </>',
            round($executionTime, 2),
            round($memoryUsed / 1024 / 1024, 2),
            array_sum(array_map('count', $res))
        ));
        $this->newLine();
    }
}
